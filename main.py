#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小马助教系统主启动文件
"""

import os
import sys
from PyQt5.QtWidgets import QApplication, QDialog
from PyQt5.QtCore import Qt
from core.login_window import LoginWindow
from core.dashboard import Dashboard
from core.login_manager import get_login_manager


def main():
    """主启动函数"""
    # 高DPI支持（必须在创建QApplication之前设置）
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("小马助教系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("小马助教")

    # 获取登录管理器
    login_manager = get_login_manager()

    # 检查是否已有有效的登录会话
    if login_manager.is_logged_in:
        print(f"检测到已有登录会话: {login_manager.teacher_info.get('teacher_name', '未知教师')}")
        # 直接进入仪表盘
        dashboard = Dashboard(teacher_info=login_manager.teacher_info)
        dashboard.show()
        return app.exec_()

    # 显示登录窗口
    login_window = LoginWindow()
    teacher_info = None

    def on_login_success(info):
        """登录成功回调函数"""
        nonlocal teacher_info
        teacher_info = info
        # 同时更新登录管理器
        login_manager.teacher_info = info
        login_manager.session = info.get('session')
        login_manager.is_logged_in = True
        login_manager.save_session()

    # 连接登录成功信号
    login_window.login_success.connect(on_login_success)

    # 显示登录窗口并等待用户操作
    login_result = login_window.exec_()

    # 检查登录结果
    if login_result == QDialog.Accepted and teacher_info:
        # 登录成功，进入仪表盘，传递教师信息
        dashboard = Dashboard(teacher_info=teacher_info)
        dashboard.show()

        # 进入应用程序主循环
        return app.exec_()
    else:
        # 登录失败或用户取消，退出程序
        return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
