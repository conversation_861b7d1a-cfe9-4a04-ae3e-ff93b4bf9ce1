import sys
import socket
import socketserver
import threading
import os
import qrcode
from PIL import Image
from PyQt5.QtWidgets import *
from PyQt5.QtGui import QPixmap, QMouseEvent
from PyQt5.QtCore import Qt, QPoint
from http.server import SimpleHTTPRequestHandler
import logging
import shutil


class CustomHandler(SimpleHTTPRequestHandler):
    def handle_one_request(self):
        try:
            super().handle_one_request()
        except ConnectionResetError:
            logging.warning("客户端断开了连接")
        except Exception as e:
            logging.error(f"处理请求时发生错误: {e}")

    def copyfile(self, source, outputfile):
        try:
            # 增加缓冲区大小
            shutil.copyfileobj(source, outputfile,
                               length=16 * 1024 * 1024)  # 16MB 缓冲区
        except ConnectionResetError:
            logging.warning("客户端断开了连接")
        except Exception as e:
            logging.error(f"文件传输失败: {e}")


class Filesharing:
    server = None
    shared_folder = ""  # 初始化为空
    server_thread = None
    PORT = 8001  # 默认端口号

    @staticmethod
    def get_local_ip():
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            s.connect(("**************", 1))
            IP = s.getsockname()[0]
        except OSError as e:
            print(f"Error retrieving local IP: {e}")
            IP = "127.0.0.1"
        finally:
            s.close()
        return IP

    @staticmethod
    def is_port_in_use(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(("localhost", port)) == 0

    @classmethod
    def start_server(cls):
        cls.PORT = 8001

        # Check and find a free port
        while cls.is_port_in_use(cls.PORT):
            cls.PORT += 1

        os.chdir(cls.shared_folder)
        cls.server = socketserver.TCPServer(("", cls.PORT), CustomHandler)
        cls.server_thread = threading.Thread(target=cls.server.serve_forever)
        cls.server_thread.daemon = True
        cls.server_thread.start()
        print(f"Server started at http://{cls.get_local_ip()}:{cls.PORT}")

    @classmethod
    def stop_server(cls):
        if cls.server:
            try:
                cls.server.shutdown()
                cls.server.server_close()
                print("Server stopped successfully.")
            except Exception as e:
                print(f"Error stopping server: {e}")

    @classmethod
    def close_server(cls):
        cls.stop_server()
        if cls.server_thread:
            cls.server_thread.join(1)  # 设置超时时间
            if cls.server_thread.is_alive():
                print("Forcefully stopping server.")
                cls.server_thread._stop()

    @classmethod
    def generate_qr_code(cls):
        """生成二维码并保存为图片"""
        ip_address = cls.get_local_ip()
        url = f"http://{ip_address}:{cls.PORT}"
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        # 使用 LANCZOS 替代 ANTIALIAS
        img = img.resize((200, 200), Image.Resampling.LANCZOS)
        img.save("qr_code.png")

        return url, "qr_code.png"


class FileSharingApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("文件共享工具")
        self.setGeometry(600, 600, 400, 400)
        self.setWindowFlags(Qt.FramelessWindowHint |
                            Qt.Tool | self.windowFlags())  # 无边框窗口
        self.setStyleSheet("background-color: #f0f0f0;")  # 设置背景颜色
        self.dragging = False
        self.offset = QPoint()

        # 设置共享文件夹为程序路径下的 record 文件夹
        self.shared_folder = os.path.join(os.path.dirname(__file__), "record")
        if not os.path.exists(self.shared_folder):
            os.makedirs(self.shared_folder)

        # 创建二维码标签
        self.qr_label = QLabel(self)
        self.qr_label.setAlignment(Qt.AlignCenter)

        # 布局
        layout = QVBoxLayout()
        layout.addWidget(self.qr_label)
        layout.setContentsMargins(20, 20, 20, 20)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

        # 启动文件共享服务并显示二维码
        self.start_sharing()

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件，用于拖动窗口"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.offset = event.globalPos() - self.pos()

    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件，用于拖动窗口"""
        if self.dragging:
            self.move(event.globalPos() - self.offset)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件，停止拖动"""
        if event.button() == Qt.LeftButton:
            self.dragging = False

    def start_sharing(self):
        """启动文件共享服务"""
        Filesharing.shared_folder = self.shared_folder
        Filesharing.start_server()
        self.update_qr_code()

    def update_qr_code(self):
        """更新并显示二维码"""
        if Filesharing.server is None:
            QMessageBox.warning(self, "警告", "请先启动文件共享服务")
            return

        url, qr_path = Filesharing.generate_qr_code()
        qr_pixmap = QPixmap(qr_path)
        self.qr_label.setPixmap(qr_pixmap)

        # 调整窗口大小以适应二维码
        self.resize(240, 240)  # 二维码大小为 200x200，留出 20px 的边距


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)  # 配置日志记录
    app = QApplication(sys.argv)
    window = FileSharingApp()
    window.show()
    sys.exit(app.exec_())
