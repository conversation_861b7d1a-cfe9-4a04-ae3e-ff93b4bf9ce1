import sys
from datetime import datetime, timedelta
from configparser import ConfigParser
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from SysCss import MyCss
from Screen_Broadcasting import BroadcastApp
from Screen_DLAN import <PERSON>D<PERSON><PERSON>
from File_sharing import FileSharingApp
from White_board import Whiteboard
from Select_People import RandomStudentPicker
#from Chat_DS import DeepSeekVoiceAssistant
from Open_Platform import OpenPlatformApp
# from old.modules.Group_screen import Groupscreen
# from Class_Quiz import EmailSenderApp
from Danma_ku import DanmakuTeacherApp
from Control_PC import ControlPCAPP
# from AI_Attendance import AttendanceSystem
from registration_app import *


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置窗口尺寸
        self.screen_height = QDesktopWidget().screenGeometry().height()
        self.screen_width = QDesktopWidget().screenGeometry().width()
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool |
                            self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 初始化应用实例为 None，延迟加载
        self.white_B = None
        self.screen_B = None
        self.screen_D = None
        self.File_S = None
        self.Select_P = None
        self.chat = None
        self.Open_P = None
        self.Group_s = None
        self.Class_Q = None
        self.Danma_k = None
        self.Control_P = None
        self.Attendance_S = None

        # 注册状态和试用期
        self.registered = False
        self.trial_period = False
        self.trial_start_date = None
        self.trial_end_date = None
        self.hardware_fingerprint = None
        self.load_registration_state()  # 加载注册状态

        # 初始化倒计时
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer)
        self.remaining_time = 3600  # 倒计时时间（秒）
        # 启动倒计时
        self.timer.start(1000)  # 每秒更新一次

        # 导航菜单层
        self.Navigation_Menu = QWidget(self)
        self.Navigation_Menu.setGeometry(
            self.screen_width - 200, (self.screen_height - 600) // 2, 200, 600)
        self.Navigation_Menu.setObjectName("Navigation_Menu")
        self.Navigation_Menu.setStyleSheet(MyCss.mainBgcolora)
        self.Navigation_Menu.setVisible(False)  # 初始设置为不可见

        # 使用 QVBoxLayout 布局（垂直布局）
        self.vertical_layout = QVBoxLayout(self.Navigation_Menu)
        self.vertical_layout.setSpacing(2)
        self.vertical_layout.setContentsMargins(10, 5, 10, 5)

        # 第一行：倒计时显示
        self.timer_widget = QWidget(self.Navigation_Menu)
        self.timer_widget.setFixedHeight(100)  # 设置高度为 100
        self.timer_layout = QVBoxLayout(self.timer_widget)
        self.timer_layout.setSpacing(0)
        self.timer_layout.setContentsMargins(0, 0, 0, 0)

        self.timer_label = QLabel("00:00", self.timer_widget)
        self.timer_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        self.timer_label.setStyleSheet(
            "font-size: 18px; font-weight: bold; color: white;")  # 设置样式
        self.timer_layout.addWidget(self.timer_label)
        self.vertical_layout.addWidget(self.timer_widget)

        # 第二行：按钮布局
        self.button_widget = QWidget(self.Navigation_Menu)
        self.button_layout = QGridLayout(self.button_widget)
        self.button_layout.setSpacing(2)
        self.button_layout.setContentsMargins(0, 0, 0, 0)

        # 按钮状态字典，记录每个按钮的选中状态
        self.button_states = {}
        buttons = [
            ("无感签到", self.show_attendance, "AI人脸无感识别签到"),
            ("电子批注", self.show_whiteboard, "打开电子批注"),
            ("本机投屏", self.show_screen_dlan, "将屏幕投放到其他设备"),
            ("屏幕广播", self.show_screen_broadcast, "广播屏幕内容"),
            ("多屏互动", self.show_group_screen, "与分组屏幕互动"),
            ("开启弹幕", self.show_danmaku, "分组端发送弹幕"),
            ("随机选人", self.show_random_picker, "随机选人"),
            ("随堂测验", self.show_class_quiz, "课堂测试或作业发布"),
            ("远程控制", self.show_control_pc, "一键开关分组屏"),
            ("课件分享", self.show_file_sharing, "扫码下载"),
            ("AI小助手", self.show_chat_assistant, "deepseek对话"),
            ("资源云盘", self.show_open_platform, "可视化资源平台"),
            ("系统注册", self.show_registration, "注册系统"),
            ("退出系统", self.CloseBotton, "退出系统"),
        ]

        # 将按钮添加到网格布局中
        for i, (text, callback, tooltip) in enumerate(buttons):
            row = i // 2  # 行号
            col = i % 2   # 列号
            button = QPushButton(text, self.button_widget)
            button.setFixedSize(64, 50)  # 设置按钮大小
            button.setStyleSheet(MyCss.butBCss)  # 按钮样式
            button.setToolTip(tooltip)  # 设置悬停提示
            button.clicked.connect(
                self.create_button_click_handler(button, text, callback))
            self.button_layout.addWidget(button, row, col)
            self.button_states[text] = False  # 初始化按钮状态为未选中

        self.vertical_layout.addWidget(self.button_widget)

        # 第三行：显示 Logo
        self.logo_widget = QWidget(self.Navigation_Menu)
        self.logo_widget.setFixedHeight(100)  # 设置高度为 100

        self.logo_layout = QVBoxLayout(self.logo_widget)
        self.logo_layout.setSpacing(0)
        self.logo_layout.setContentsMargins(0, 0, 0, 0)

        # 创建 QLabel 用于显示 Logo
        self.logo_label = QLabel(self.logo_widget)
        self.logo_label.setAlignment(Qt.AlignCenter)  # 居中显示 Logo

        # 加载 Logo 图片
        logo_pixmap = QPixmap("images/logo.png")  # 替换为你的 Logo 路径
        self.logo_label.setPixmap(logo_pixmap.scaled(
            100, 100, Qt.KeepAspectRatio))  # 缩放 Logo 到合适大小

        # 将 Logo 添加到布局
        self.logo_layout.addWidget(self.logo_label)

        # 将 Logo 组件添加到垂直布局
        self.vertical_layout.addWidget(self.logo_widget)

        # 箭头按钮
        self.arrow_button = QPushButton("→", self)
        self.arrow_button.setGeometry(
            self.screen_width - 30, (self.screen_height) // 2, 50, 50)
        self.arrow_button.clicked.connect(self.toggle_menu)

        # 动画效果
        self.animation = QPropertyAnimation(self.Navigation_Menu, b"geometry")
        self.animation.setDuration(5000)  # 动画持续时间

        # 检查试用期或注册状态
        self.check_trial_or_registration()

    def check_trial_or_registration(self):
        """检查试用期或注册状态，并在启动时提示用户"""
        if not self.registered and not self.trial_period:
            # 首次启动，初始化试用期
            self.trial_period = True
            self.trial_start_date = datetime.now().strftime("%Y-%m-%d")
            self.trial_end_date = (
                datetime.now() + timedelta(days=90)).strftime("%Y-%m-%d")
            self.save_registration_state()
            QMessageBox.information(
                self, "试用期开始", f"试用期已开始，结束日期为 {self.trial_end_date}！")
        elif self.trial_period and self.trial_end_date:
            trial_end = datetime.strptime(self.trial_end_date, "%Y-%m-%d")
            if datetime.now() > trial_end:
                self.trial_period = False
                QMessageBox.information(self, "试用期结束", "试用期已结束，请注册以继续使用！")

    def create_button_click_handler(self, button, text, callback):
        """创建按钮点击事件处理函数"""
        def handler():
            if self.button_states[text]:  # 如果按钮已选中
                button.setStyleSheet(MyCss.butBCss)  # 恢复原状
                self.button_states[text] = False  # 更新状态为未选中
                # 关闭对应的功能模块
                if text == "电子批注" and self.white_B:
                    self.white_B.close()
                elif text == "无感签到" and self.Attendance_S:
                    self.Attendance_S.close()
                elif text == "本机投屏" and self.screen_D:
                    self.screen_D.close()
                elif text == "屏幕广播" and self.screen_B:
                    self.screen_B.close()
                elif text == "多屏互动" and self.Group_s:
                    self.Group_s.close()
                elif text == "开启弹幕" and self.Danma_k:
                    self.Danma_k.close()
                elif text == "随机选人" and self.Select_P:
                    self.Select_P.close()
                elif text == "随堂测验" and self.Class_Q:
                    self.Class_Q.close()
                elif text == "远程控制" and self.Control_P:
                    self.Control_P.close()
                elif text == "课件分享" and self.File_S:
                    self.File_S.close()
                elif text == "AI小助手" and self.chat:
                    self.chat.close()
                elif text == "资源云盘" and self.Open_P:
                    self.Open_P.close()
            else:
                # 检查是否为试用期内
                if self.trial_period and self.trial_end_date:
                    trial_end = datetime.strptime(
                        self.trial_end_date, "%Y-%m-%d")
                    if datetime.now() <= trial_end:
                        # 试用期内，直接执行功能
                        button.setStyleSheet(
                            "background-color: red; color: white;")  # 设置为红色
                        self.button_states[text] = True  # 更新状态为选中
                        callback()  # 执行回调函数
                        return
                    else:
                        # 试用期已结束
                        self.trial_period = False
                        QMessageBox.information(
                            self, "试用期结束", "试用期已结束，请注册以继续使用！")
                        return

                # 非试用期或试用期已结束，检查注册状态
                if text != "系统注册" and text != "退出系统" and not self.registered:
                    QMessageBox.warning(self, "未注册", "请先注册以解锁功能！")
                    return
                button.setStyleSheet(
                    "background-color: red; color: white;")  # 设置为红色
                self.button_states[text] = True  # 更新状态为选中
                callback()  # 执行回调函数
        return handler

    ############### 注册信息函数 #####################################################
    def load_registration_state(self):
        """从配置文件加载注册状态、试用期和硬件指纹"""
        config = ConfigParser()
        try:
            config.read('registration.ini')
            if 'Registration' in config:
                self.registered = config.getboolean(
                    'Registration', 'Registered')
                self.trial_period = config.getboolean(
                    'Registration', 'TrialPeriod', fallback=False)
                self.trial_start_date = config.get(
                    'Registration', 'TrialStartDate', fallback=None)
                self.trial_end_date = config.get(
                    'Registration', 'TrialEndDate', fallback=None)
                self.hardware_fingerprint = config.get(
                    'Registration', 'HardwareFingerprint', fallback=None)

                # 验证硬件指纹是否匹配
                current_fingerprint = generate_hardware_fingerprint()
                if self.hardware_fingerprint != current_fingerprint:
                    self.registered = False
                    self.trial_period = False
                    self.hardware_fingerprint = None
                    self.save_registration_state()  # 清除不匹配的注册信息
                    QMessageBox.warning(self, "警告", "硬件指纹不匹配，请重新注册！")
                    return

                # 检查试用期是否已过期
                if self.trial_period and self.trial_end_date:
                    trial_end = datetime.strptime(
                        self.trial_end_date, "%Y-%m-%d")
                    if datetime.now() > trial_end:
                        self.trial_period = False
                        self.save_registration_state()  # 更新试用期状态
                        QMessageBox.information(
                            self, "试用期结束", "试用期已结束，请注册以继续使用！")
        except Exception as e:
            print(f"加载注册状态失败: {e}")
            self.registered = False
            self.trial_period = False

    def save_registration_state(self):
        """保存注册状态、试用期和硬件指纹到配置文件"""
        config = ConfigParser()
        config['Registration'] = {
            'Registered': str(self.registered),
            'TrialPeriod': str(self.trial_period),
            'TrialStartDate': self.trial_start_date if self.trial_start_date else "",
            'TrialEndDate': self.trial_end_date if self.trial_end_date else "",
            'HardwareFingerprint': self.hardware_fingerprint if self.hardware_fingerprint else ""
        }
        try:
            with open('registration.ini', 'w') as configfile:
                config.write(configfile)
        except Exception as e:
            print(f"保存注册状态失败: {e}")

    def show_registration(self):
        """显示注册模块"""
        if self.registered:
            QMessageBox.information(self, "已注册", "系统已注册，无需重复操作！")
            return

        if self.trial_period:
            trial_end = datetime.strptime(self.trial_end_date, "%Y-%m-%d")
            if datetime.now() <= trial_end:
                QMessageBox.information(
                    self, "试用中", f"试用期剩余 {(trial_end - datetime.now()).days} 天！")
                return

        # 生成并保存硬件指纹
        user_client = UserClient()
        self.hardware_fingerprint = generate_hardware_fingerprint()
        self.save_registration_state()

        # 显示硬件指纹
        QMessageBox.information(
            self, "硬件指纹", f"您的硬件指纹为：\n{self.hardware_fingerprint}")

        # 输入注册码
        code, ok = QInputDialog.getText(self, "注册", "请输入注册码：")
        if ok:
            expected_code = user_client.generate_registration_code(
                self.hardware_fingerprint)
            if code == expected_code:
                self.registered = True
                self.trial_period = False
                self.save_registration_state()
                QMessageBox.information(self, "注册成功", "注册成功，功能已解锁！")
            else:
                QMessageBox.warning(self, "注册失败", "注册码错误，请重试！")

    ############### 功能模块函数 #####################################################
    def show_whiteboard(self):
        """显示电子白板"""
        if self.white_B is None:
            self.white_B = Whiteboard()
        self.white_B.showFullScreen()

    def show_attendance(self):
        """显示无感签到"""
        if self.Attendance_S is None:
            self.Attendance_S = AttendanceSystem()
        self.Attendance_S.show()

    def show_screen_dlan(self):
        """显示本机投屏"""
        if self.screen_D is None:
            self.screen_D = ScreenDLAN()
        self.screen_D.show()

    def show_screen_broadcast(self):
        """显示屏幕广播"""
        if self.screen_B is None:
            self.screen_B = BroadcastApp()
        self.screen_B.show()

    def show_group_screen(self):
        """显示多屏互动"""
        if self.Group_s is None:
            self.Group_s = Groupscreen()
        self.Group_s.show()

    def show_danmaku(self):
        """显示弹幕功能"""
        if self.Danma_k is None:
            self.Danma_k = DanmakuTeacherApp()
        self.Danma_k.showFullScreen()

    def show_random_picker(self):
        """显示随机选人"""
        if self.Select_P is None:
            self.Select_P = RandomStudentPicker()
        self.Select_P.show()

    def show_class_quiz(self):
        """显示随堂测验"""
        if self.Class_Q is None:
            self.Class_Q = EmailSenderApp()
        self.Class_Q.show()

    def show_control_pc(self):
        """显示远程控制"""
        if self.Control_P is None:
            self.Control_P = ControlPCAPP()
        self.Control_P.show()

    def show_file_sharing(self):
        """显示课件分享"""
        if self.File_S is None:
            self.File_S = FileSharingApp()
        self.File_S.show()

    def show_chat_assistant(self):
        """显示AI小助手"""
        if self.chat is None:
            self.chat = DeepSeekVoiceAssistant()
        self.chat.show()

    def show_open_platform(self):
        """显示资源云盘"""
        if self.Open_P is None:
            self.Open_P = OpenPlatformApp()
        self.Open_P.show()

    def toggle_menu(self):
        """切换导航菜单的显示和隐藏"""
        if self.Navigation_Menu.isVisible():
            # 隐藏菜单
            self.animation.setStartValue(self.Navigation_Menu.geometry())
            self.animation.setEndValue(
                QRect(self.screen_width - 200, (self.screen_height - 600) // 2, 200, 600))
            self.animation.start()
            self.Navigation_Menu.setVisible(False)  # 设置为不可见
        else:
            # 显示菜单
            self.Navigation_Menu.setVisible(True)  # 设置为可见
            self.animation.setStartValue(
                QRect(self.screen_width - 200, (self.screen_height - 600) // 2, 200, 600))
            self.animation.setEndValue(
                QRect(self.screen_width - 200, (self.screen_height - 600) // 2, 200, 600))
            self.animation.start()

    def hide_menu(self):
        """自动隐藏菜单"""
        if self.Navigation_Menu.isVisible():
            self.animation.setStartValue(self.Navigation_Menu.geometry())
            self.animation.setEndValue(
                QRect(self.screen_width - 200, (self.screen_height - 600) // 2, 200, 600))
            self.animation.start()
            self.Navigation_Menu.setVisible(False)  # 设置为不可见

    def update_timer(self):
        """更新倒计时显示"""
        if self.remaining_time > 0:
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"{minutes:02}:{seconds:02}")
            self.remaining_time -= 1
        else:
            self.timer.stop()
            self.timer_label.setText("时间到！")

    def CloseBotton(self):
        """退出系统提示"""
        message_box = QMessageBox(self)
        message_box.setWindowTitle("消息提示")
        message_box.setText("您确定退出系统么？如果确定请点击[OK]")
        message_box.setIcon(QMessageBox.Information)
        message_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        returnValue = message_box.exec()
        if returnValue == QMessageBox.Ok:
            app.quit()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.showFullScreen()
    sys.exit(app.exec_())
