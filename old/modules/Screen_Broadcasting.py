import sys
import subprocess
import re
import shutil
import platform
from threading import Thread
from PyQt5.QtWidgets import *
from PyQt5.QtCore import Qt, pyqtSignal, QObject


class BroadcastApp(QWidget):
    log_signal = pyqtSignal(str)  # 定义信号，用于传递日志消息

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.process = None

        # 连接信号槽
        self.log_signal.connect(self.log_output.append)

    def init_ui(self):
        self.setWindowTitle("桌面广播工具")
        self.setGeometry(800, 400, 300, 400)
        # self.setWindowFlags(Qt.FramelessWindowHint)  # 无边框窗口
        self.setWindowFlags(Qt.Tool | self.windowFlags())

       
        # 主布局
        main_layout = QVBoxLayout()

        # 目标地址组
        target_group = QGroupBox("目标地址")
        target_layout = QVBoxLayout()
        self.target_input = QLineEdit()
        self.target_input.setPlaceholderText("例如：************* 或 *********")
        target_layout.addWidget(self.target_input)
        target_group.setLayout(target_layout)
        main_layout.addWidget(target_group)

        # 广播模式组
        mode_group = QGroupBox("广播模式")
        mode_layout = QHBoxLayout()
        self.specific_radio = QRadioButton("指定IP地址")
        self.broadcast_radio = QRadioButton("向所有电脑广播")
        self.specific_radio.setChecked(True)
        mode_layout.addWidget(self.specific_radio)
        mode_layout.addWidget(self.broadcast_radio)
        mode_group.setLayout(mode_layout)
        main_layout.addWidget(mode_group)

        # 端口组
        port_group = QGroupBox("端口号")
        port_layout = QVBoxLayout()
        self.port_input = QLineEdit()
        self.port_input.setPlaceholderText("例如：8080")
        port_layout.addWidget(self.port_input)
        port_group.setLayout(port_layout)
        main_layout.addWidget(port_group)

        # 控制按钮组
        button_group = QGroupBox("控制")
        button_layout = QHBoxLayout()
        self.start_button = QPushButton("开始广播")
        self.stop_button = QPushButton("停止广播")
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_group.setLayout(button_layout)
        main_layout.addWidget(button_group)

        # 日志组
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        log_layout.addWidget(self.log_output)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 设置主布局
        self.setLayout(main_layout)

        # 信号槽连接
        self.start_button.clicked.connect(self.start_broadcast)
        self.stop_button.clicked.connect(self.stop_broadcast)
        self.broadcast_radio.toggled.connect(self.update_target_input)

    def update_target_input(self):
        """根据广播模式更新目标地址输入框"""
        if self.broadcast_radio.isChecked():
            self.target_input.setText("*********")  # 使用组播地址
            self.target_input.setEnabled(False)
        else:
            self.target_input.clear()
            self.target_input.setEnabled(True)

    def is_valid_ip(self, ip):
        """验证IP地址是否合法"""
        pattern = r"^(\d{1,3}\.){3}\d{1,3}$"
        if re.match(pattern, ip):
            return all(0 <= int(part) <= 255 for part in ip.split("."))
        return False

    def is_valid_port(self, port):
        """验证端口号是否合法"""
        try:
            port = int(port)
            return 0 <= port <= 65535
        except ValueError:
            return False

    def is_ffmpeg_available(self):
        """检查ffmpeg是否可用"""
        return shutil.which("ffmpeg") is not None

    def get_capture_device(self):
        """根据操作系统获取捕获设备"""
        system = platform.system()
        if system == "Windows":
            return "gdigrab"
        elif system == "Linux":
            return "x11grab"
        elif system == "Darwin":
            return "avfoundation"
        else:
            return None

    def log_ffmpeg_output(self):
        """捕获并显示ffmpeg的输出"""
        while self.process:
            output = self.process.stderr.readline()
            if output:
                self.log_signal.emit(output.decode().strip())  # 发射信号

    def start_broadcast(self):
        """启动广播"""
        target = self.target_input.text()
        port = self.port_input.text()

        # 输入验证
        if not target or not port:
            self.log_signal.emit("错误：请填写目标地址和端口号。")
            return
        if not self.is_valid_ip(target):
            self.log_signal.emit("错误：目标地址不是有效的IP地址。")
            return
        if not self.is_valid_port(port):
            self.log_signal.emit("错误：端口号无效。")
            return
        if not self.is_ffmpeg_available():
            self.log_signal.emit("错误：未找到ffmpeg，请确保已安装并配置。")
            return

        # 获取捕获设备
        capture_device = self.get_capture_device()
        if not capture_device:
            self.log_signal.emit("错误：不支持当前操作系统。")
            return

        # 构建ffmpeg命令
        command = [
            "ffmpeg",
            '-re',  # 实时模式
            "-f", capture_device,  # 捕获设备
            "-framerate", "30",  # 帧率
            "-i", "desktop",  # 输入为桌面
            "-c:v", "libx264",  # 视频编码器
            "-f", "mpegts",  # 输出格式
            f"udp://{target}:{port}?pkt_size=1316",  # 目标地址，设置UDP包大小
        ]

        # 启动ffmpeg进程
        self.process = subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )

        # 启动日志线程
        log_thread = Thread(target=self.log_ffmpeg_output, daemon=True)
        log_thread.start()

        # 更新界面状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.log_signal.emit(f"开始广播到 {target}:{port}")

    def stop_broadcast(self):
        """停止广播"""
        if self.process:
            self.process.terminate()
            self.process.wait()
            self.process = None

        # 更新界面状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.log_signal.emit("广播已停止。")

    def closeEvent(self, event):
        """关闭窗口时停止广播"""
        self.stop_broadcast()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BroadcastApp()
    # window.setWindowTitle("桌面广播工具")
    window.show()
    sys.exit(app.exec_())
