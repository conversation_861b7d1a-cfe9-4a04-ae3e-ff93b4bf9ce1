import sys
import os
import json
import cv2
import face_recognition
import numpy as np
from datetime import datetime, time
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PIL import Image, ImageDraw, ImageFont


# 数据库目录
DATABASE_DIR = "database"

# 学生表 JSON 文件路径
STUDENTS_JSON = os.path.join(DATABASE_DIR, "students.json")
ATTENDANCE_JSON = os.path.join(DATABASE_DIR, "attendance.json")

# 人脸照片目录
FACE_IMAGE_DIR = os.path.join(DATABASE_DIR, "img")

# 考勤时间设置
START_TIME = time(8, 0)  # 上课时间
END_TIME = time(17, 0)  # 下课时间


class AttendanceSystem(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("人脸识别考勤系统")
        self.setGeometry(600, 100, 600, 900)
        self.setWindowFlags(Qt.Tool | self.windowFlags())


        # 加载学生表
        self.students = self.load_students()
        self.attendance = {}

        # 初始化界面
        self.init_ui()

        # 初始化摄像头
        self.cap = cv2.VideoCapture(0)

        # 设置采集尺寸（宽度和高度）
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)  # 设置宽度为1280像素
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)  # 设置高度为720像素

        self.timer = self.startTimer(30)  # 每 30ms 更新一次摄像头画面

    def init_ui(self):
        """初始化界面"""
        # 主布局
        main_layout = QVBoxLayout()

        # 摄像头画面
        self.camera_label = QLabel(self)
        main_layout.addWidget(self.camera_label)

        # 统计图表
        self.figure = Figure()
        self.canvas = FigureCanvas(self.figure)
        main_layout.addWidget(self.canvas)

        # 签到信息表格
        self.table = QTableWidget(self)
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["学号", "姓名", "签到信息"])
        self.table.setColumnWidth(0, 100)
        self.table.setColumnWidth(1, 150)
        self.table.setColumnWidth(2, 200)
        main_layout.addWidget(self.table)

        # 开始考勤按钮
        self.start_button = QPushButton("开始考勤", self)
        self.start_button.clicked.connect(self.start_attendance)
        main_layout.addWidget(self.start_button)

        # 主窗口
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def load_students(self):
        """加载学生表"""
        if os.path.exists(STUDENTS_JSON):
            with open(STUDENTS_JSON, "r", encoding="utf-8") as f:
                students_data = json.load(f)
                # 将学生表转换为字典，以 id 为键
                students = {}
                for student in students_data:
                    face_image_path = os.path.join(
                        FACE_IMAGE_DIR, student["avatar"])
                    face_encoding = self.load_face_encoding(face_image_path)
                    if face_encoding is not None:  # 跳过未检测到人脸的学生
                        students[student["id"]] = {
                            "name": student["name"],
                            "avatar": face_image_path,
                            "face_encoding": face_encoding
                        }
                return students
        return {}

    def load_face_encoding(self, face_image_path):
        """加载人脸编码"""
        if os.path.exists(face_image_path):
            image = face_recognition.load_image_file(face_image_path)
            face_encodings = face_recognition.face_encodings(image)
            if face_encodings:
                return face_encodings[0].tolist()
            else:
                print(f"警告：未检测到人脸，文件路径：{face_image_path}")
                return None
        else:
            print(f"错��：文件不存在，路径：{face_image_path}")
            return None

    def save_attendance(self):
        """保存考勤记录"""
        with open(ATTENDANCE_JSON, "w", encoding="utf-8") as f:
            json.dump(self.attendance, f, ensure_ascii=False, indent=4)

    def recognize_face(self, frame):
        """人脸识别"""
        # 检测人脸
        face_locations = face_recognition.face_locations(frame)
        face_encodings = face_recognition.face_encodings(frame, face_locations)

        for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
            # 比对学生表
            for student_id, student_data in self.students.items():
                known_face_encoding = np.array(student_data["face_encoding"])
                if known_face_encoding.size == 0:  # 跳过无效的人脸编码
                    continue
                matches = face_recognition.compare_faces(
                    [known_face_encoding], face_encoding)

                if matches[0]:
                    # 记录考勤
                    self.record_attendance(student_id)
                    # 将 OpenCV 图像转换为 PIL 图像
                    frame_pil = Image.fromarray(
                        cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    draw = ImageDraw.Draw(frame_pil)
                    # 加载中文字体
                    # font_path = "usr/share/fonts/fonts-gb/GB_FS_GB18030.ttf"  # 确保字体文件存在
                    font = ImageFont.truetype("/usr/share/fonts/custom/wqy-zenhei.ttc", 24)
                    # font = ImageFont.truetype(font_path, 24)
                    # 绘制中文姓名
                    draw.text((left, top - 30),
                              student_data["name"], font=font, fill=(0, 255, 0))
                    # 将 PIL 图像转换回 OpenCV 图像
                    frame = cv2.cvtColor(
                        np.array(frame_pil), cv2.COLOR_RGB2BGR)
                    # 绘制人脸框
                    cv2.rectangle(frame, (left, top),
                                  (right, bottom), (0, 255, 0), 2)
                    break

        return frame

    def record_attendance(self, student_id):
        """记录考勤"""
        if student_id not in self.attendance:
            now = datetime.now()
            status = "正常"
            if now.time() < START_TIME:
                status = "迟到"
            elif now.time() > END_TIME:
                status = "早退"
            self.attendance[student_id] = {
                "name": self.students[student_id]["name"],
                "time": now.strftime("%Y-%m-%d %H:%M:%S"),
                "status": status
            }
            self.save_attendance()
            self.update_table()

    def start_attendance(self):
        """开始考勤"""
        self.attendance = {}
        self.save_attendance()
        self.update_table()

    def timerEvent(self, event):
        """定时器事件，更新摄像头画面"""
        ret, frame = self.cap.read()
        if ret:
            frame = self.recognize_face(frame)
            self.display_frame(frame)
            self.update_statistics()

    def display_frame(self, frame):
        """显示摄像头画面"""
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = frame.shape
        bytes_per_line = ch * w
        q_img = QImage(frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
        self.camera_label.setPixmap(QPixmap.fromImage(q_img))

    def update_statistics(self):
        """更新考勤统计图表"""
        import matplotlib.pyplot as plt

        # 设置支持中文的字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

        total_students = len(self.students)
        present_students = len(self.attendance)
        absent_students = total_students - present_students
        late_students = sum(
            1 for record in self.attendance.values() if record["status"] == "迟到")
        early_students = sum(
            1 for record in self.attendance.values() if record["status"] == "早退")

        # 清除旧图表
        self.figure.clear()

        # 绘制饼形图
        ax = self.figure.add_subplot(111)
        labels = ["实到人数", "缺勤人数", "迟到人数", "早退人数"]
        sizes = [present_students, absent_students,
                 late_students, early_students]
        colors = ["green", "red", "orange", "purple"]
        ax.pie(sizes, labels=labels, colors=colors,
               autopct="%1.1f%%", startangle=90)
        ax.axis("equal")  # 保持圆形
        ax.set_title("考勤统计")

        # 刷新画布
        self.canvas.draw()

    def update_table(self):
        """更新签到信息表格"""
        self.table.setRowCount(len(self.attendance))
        for row, (student_id, record) in enumerate(self.attendance.items()):
            self.table.setItem(row, 0, QTableWidgetItem(student_id))
            self.table.setItem(row, 1, QTableWidgetItem(record["name"]))
            self.table.setItem(row, 2, QTableWidgetItem(
                f"{record['time']} ({record['status']})"))

    def closeEvent(self, event):
        """关闭窗口时释放摄像头"""
        self.cap.release()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = AttendanceSystem()
    window.show()
    sys.exit(app.exec_())
