import sys
import os
import subprocess
import time
from datetime import datetime
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtWidgets import QApplication, QWidget, QPushButton, QHBoxLayout, QVBoxLayout, QLabel, QMessageBox


class ScreenRecorder(QWidget):
    def __init__(self):
        super().__init__()
        self.recording_process = None
        self.is_recording = False
        self.is_paused = False
        self.start_time = None
        self.elapsed_time = 0
        self.output_file = None
        self.initUI()

    def initUI(self):
        """初始化用户界面"""
        self.setWindowTitle("屏幕录制")
        self.setGeometry(800, 600, 400, 150)
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # 录制时间显示
        self.time_label = QLabel("00:00:00", self)
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setStyleSheet("font-size: 20px; font-weight: bold;")

        # 录制按钮
        self.record_button = QPushButton("开始录制", self)
        self.record_button.setStyleSheet(self.get_button_style("green"))
        self.record_button.clicked.connect(self.toggle_recording)

        # 暂停按钮
        self.pause_button = QPushButton("暂停", self)
        self.pause_button.setStyleSheet(self.get_button_style("orange"))
        self.pause_button.clicked.connect(self.toggle_pause)
        self.pause_button.setEnabled(False)  # 初始状态下禁用

        # 停止按钮
        self.stop_button = QPushButton("停止", self)
        self.stop_button.setStyleSheet(self.get_button_style("red"))
        self.stop_button.clicked.connect(self.stop_recording)
        self.stop_button.setEnabled(False)  # 初始状态下禁用

        # 横向布局按钮
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.record_button)
        button_layout.addWidget(self.pause_button)
        button_layout.addWidget(self.stop_button)

        # 主垂直布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.time_label)
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

        # 定时器，用于更新录制时间
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_time)

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""  
            QPushButton {{  
                background-color: {color};  
                color: white;  
                border-radius: 5px;  
                padding: 10px;  
                min-width: 100px;  
            }}  
            QPushButton:hover {{  
                background-color: dark{color};  
            }}  
            QPushButton:pressed {{  
                background-color: light{color};  
            }}  
        """

    def toggle_recording(self):
        """开始或暂停录制"""
        if not self.is_recording:
            self.start_recording()
        else:
            self.toggle_pause()

    def start_recording(self):
        """开始录制"""
        try:
            output_dir = os.path.join(os.getcwd(), "record")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_file = os.path.join(output_dir, f"{timestamp}.flv")

            # 优化后的FFmpeg命令
            command = [
                'ffmpeg',
                '-f', 'gdigrab',
                '-framerate', '30',
                '-i', 'desktop',
                '-c:v', 'libx264',
                '-preset', 'fast',
                '-pix_fmt', 'yuv420p',
                '-b:v', '3M',
                '-f', 'flv',  # 使用 FLV 格式
                self.output_file
            ]

            # 启动FFmpeg进程（隐藏控制台窗口）
            self.recording_process = subprocess.Popen(
                command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW  # Windows隐藏窗口
            )
            self.is_recording = True
            self.start_time = time.time()
            self.timer.start(1000)  # 启动定时器，每秒更新一次时间

            # 更新按钮状态
            self.record_button.setText("暂停")
            self.record_button.setStyleSheet(self.get_button_style("orange"))
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"录制启动失败: {str(e)}")
    def toggle_pause(self):
        """暂停或继续录制"""
        try:
            if self.is_paused:
                # 继续录制
                self.recording_process.send_signal(
                    subprocess.signal.CTRL_C_EVENT)
                self.is_paused = False
                self.pause_button.setText("暂停")
            else:
                # 暂停录制
                self.recording_process.send_signal(
                    subprocess.signal.CTRL_C_EVENT)
                self.is_paused = True
                self.pause_button.setText("继续")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"暂停/继续操作失败: {str(e)}")

    def stop_recording(self):
        """停止录制"""
        try:
            if self.recording_process:
                self.recording_process.terminate()  # 终止 FFmpeg 进程
                self.recording_process.wait()  # 等待进程结束

                self.recording_process = None

            self.is_recording = False
            self.is_paused = False
            self.timer.stop()

            # 更新按钮状态
            self.record_button.setText("开始录制")
            self.record_button.setStyleSheet(self.get_button_style("green"))
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)

            # 提示录制完成
            QMessageBox.information(
                self, "提示", f"录制已停止，文件保存为: {self.output_file}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止录制失败: {str(e)}")

    def update_time(self):
        """更新录制时间"""
        if self.is_recording and not self.is_paused:
            self.elapsed_time = int(time.time() - self.start_time)
            hours = self.elapsed_time // 3600
            minutes = (self.elapsed_time % 3600) // 60
            seconds = self.elapsed_time % 60
            self.time_label.setText(f"{hours:02}:{minutes:02}:{seconds:02}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    recorder = ScreenRecorder()
    recorder.show()
    sys.exit(app.exec_())

